# JJWX 信息协同处理平台 - 业务架构设计

## 业务概述
JJWX是一个信息报送与协同处理平台，支持多部门信息协同处理、信息关联管理和进展跟踪。

## 核心业务场景

### 场景1：跨部门信息报送与协同处理
**流程**：
1. 部门A报送一条不涉及自身的信息
2. 信息管理部门接收并审核
3. 审核通过后，分发给相关部门B和C
4. 部门B和C各自独立回复处理进展
5. 所有进展按时间线统一展示

**参与角色**：报送部门、管理部门、处理部门

### 场景2：自部门信息自主处理
**流程**：
1. 部门A报送涉及自身的信息
2. 信息管理部门审核（可选）
3. 部门A自主回复处理进展
4. 进展实时更新和展示

**参与角色**：报送部门、管理部门

### 场景3：外部信息录入与分发
**流程**：
1. 管理部门从外部渠道获取信息
2. 录入系统并分类
3. 分发给相关处理部门
4. 各部门协同处理

**参与角色**：管理部门、处理部门

### 场景4：重要信息上报机制
**流程**：
1. 管理部门从信息列表中筛选重要信息
2. 标记为重要并上报给上级部门
3. 上级部门查看和处理

**参与角色**：管理部门、上级部门

### 场景5：信息闭环管理
**流程**：
1. 处理部门认为信息处理完毕，申请闭环
2. 管理部门审核闭环申请
3. 审核通过后，信息状态变更为已闭环
4. 或管理部门直接判断信息处理完毕，设置闭环

**参与角色**：处理部门、管理部门

### 场景6：信息关联与进展共享
**流程**：
1. 系统智能识别或用户手动发现相关信息
2. 创建信息关联关系
3. 关联信息共享处理进展
4. 统一时间线展示所有相关进展

**参与角色**：所有相关部门

## 核心业务实体

### 1. 信息（Information）
**属性**：
- 基本信息：标题、内容、类别、优先级
- 状态信息：当前状态、处理进度
- 关联信息：报送部门、处理部门、管理部门
- 时间信息：创建时间、更新时间、截止时间

**状态流转**：
```
待审核 → 审核通过 → 处理中 → 待闭环 → 已闭环
       ↓
    审核拒绝
```

### 2. 事件（Event）
**属性**：
- 事件名称、描述、类型
- 关联的多条信息
- 事件状态和处理进度

### 3. 部门（Department）
**属性**：
- 部门名称、类型、层级关系
- 部门职责和权限范围
- 部门联系人信息

### 4. 进展（Progress）
**属性**：
- 进展内容、处理人、处理时间
- 进展类型（回复、转发、处理、闭环等）
- 关联的信息或事件

### 5. 关联关系（Association）
**属性**：
- 关联类型、关联强度、创建原因
- 关联的信息或事件
- 审批状态和审批人

## 权限模型设计

### 角色定义
1. **系统管理员**：系统配置、用户管理、权限分配
2. **信息管理员**：信息审核、分发、闭环、关联管理
3. **部门管理员**：本部门信息管理、人员管理
4. **报送人员**：信息报送、查看自己报送的信息
5. **处理人员**：处理分发的信息、回复进展
6. **查看人员**：只读查看相关信息

### 权限矩阵
| 操作 | 系统管理员 | 信息管理员 | 部门管理员 | 报送人员 | 处理人员 | 查看人员 |
|------|------------|------------|------------|----------|----------|----------|
| 信息报送 | ✓ | ✓ | ✓ | ✓ | ✓ | ✗ |
| 信息审核 | ✓ | ✓ | ✗ | ✗ | ✗ | ✗ |
| 信息分发 | ✓ | ✓ | ✗ | ✗ | ✗ | ✗ |
| 进展回复 | ✓ | ✓ | ✓ | ✓ | ✓ | ✗ |
| 信息关联 | ✓ | ✓ | 申请 | 申请 | 申请 | ✗ |
| 信息闭环 | ✓ | ✓ | 申请 | 申请 | 申请 | ✗ |
| 重要标记 | ✓ | ✓ | ✗ | ✗ | ✗ | ✗ |

### 数据权限
- **全局权限**：系统管理员、信息管理员
- **部门权限**：只能查看和处理本部门相关的信息
- **个人权限**：只能查看自己报送或被分配处理的信息
- **关联权限**：通过信息关联获得的间接查看权限

## 业务规则

### 信息处理规则
1. 信息必须经过审核才能分发（除自部门信息）
2. 分发后的信息不能撤回，只能补充说明
3. 信息闭环需要管理部门确认或处理部门申请
4. 重要信息必须在规定时间内处理

### 关联管理规则
1. 同一事件的信息可以直接关联
2. 重要关联需要管理部门审批
3. 关联后的信息共享进展但不共享权限
4. 关联关系可以解除但需要记录原因

### 进展管理规则
1. 所有处理进展必须记录在案
2. 进展内容不能删除，只能补充
3. 关联信息的进展统一展示
4. 进展查看受权限控制

## 业务流程图

### 信息处理主流程
```mermaid
graph TD
    A[信息报送] --> B{是否涉及自身}
    B -->|是| C[自主处理]
    B -->|否| D[管理部门审核]
    D --> E{审核结果}
    E -->|通过| F[分发给相关部门]
    E -->|拒绝| G[通知报送部门]
    F --> H[各部门协同处理]
    C --> I[记录处理进展]
    H --> I
    I --> J{是否完成}
    J -->|是| K[申请闭环]
    J -->|否| I
    K --> L[管理部门审核]
    L --> M[信息闭环]
```

### 信息关联流程
```mermaid
graph TD
    A[新信息创建] --> B[智能关联分析]
    B --> C[生成关联建议]
    C --> D[用户确认关联]
    D --> E{是否需要审批}
    E -->|是| F[提交审批]
    E -->|否| G[关联生效]
    F --> H{审批结果}
    H -->|通过| G
    H -->|拒绝| I[关联失败]
    G --> J[进展共享]
```

## 文档修改记录
| 版本 | 时间 | 修改人 | 修改内容 |
|------|------|--------|----------|
| v1.0 | 2025-06-04 13:14:46 +08:00 | AR,PDM | 创建业务架构设计文档 |
