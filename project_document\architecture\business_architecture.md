# JJWX 信息协同处理平台 - 业务架构设计

## 业务概述
JJWX是一个信息报送与协同处理平台，支持多部门信息协同处理、信息关联管理和进展跟踪。

## 核心业务场景

### 场景1：跨部门信息报送与协同处理
**流程**：
1. 部门A报送一条不涉及自身的信息
2. 信息管理部门接收并审核
3. 审核通过后，分发给相关部门B和C
4. 部门B和C各自独立回复处理进展
5. 所有进展按时间线统一展示

**参与角色**：报送部门、管理部门、处理部门

### 场景2：自部门信息自主处理
**流程**：
1. 部门A报送涉及自身的信息
2. 信息管理部门审核（可选）
3. 部门A自主回复处理进展
4. 进展实时更新和展示

**参与角色**：报送部门、管理部门

### 场景3：外部信息录入与分发
**流程**：
1. 管理部门从外部渠道获取信息
2. 录入系统并分类
3. 分发给相关处理部门
4. 各部门协同处理

**参与角色**：管理部门、处理部门

### 场景4：重要信息上报机制
**流程**：
1. 管理部门从信息列表中筛选重要信息
2. 标记为重要并上报给上级部门
3. 上级部门查看和处理

**参与角色**：管理部门、上级部门

### 场景5：信息闭环管理
**流程**：
1. 处理部门认为信息处理完毕，申请闭环
2. 管理部门审核闭环申请
3. 审核通过后，信息状态变更为已闭环
4. 或管理部门直接判断信息处理完毕，设置闭环

**参与角色**：处理部门、管理部门

### 场景6：信息关联与进展共享
**流程**：
1. 系统智能识别或用户手动发现相关信息
2. 创建信息关联关系
3. 关联信息共享处理进展
4. 统一时间线展示所有相关进展

**参与角色**：所有相关部门

## 核心业务实体

### 1. 信息（Information）
**属性**：
- 基本信息：标题、内容、类别、优先级
- 状态信息：当前状态、处理进度
- 关联信息：报送部门、处理部门、管理部门
- 时间信息：创建时间、更新时间、截止时间

**状态流转**：
```
待审核 → 审核通过 → 处理中 → 待闭环 → 已闭环
       ↓
    审核拒绝
```

### 2. 事件（Event）
**属性**：
- 事件名称、描述、类型
- 关联的多条信息
- 事件状态和处理进度

### 3. 部门（Department）
**属性**：
- 部门名称、类型、层级关系
- 部门职责和权限范围
- 部门联系人信息

### 4. 进展（Progress）
**属性**：
- 进展内容、处理人、处理时间
- 进展类型（回复、转发、处理、闭环等）
- 关联的信息或事件

### 5. 关联关系（Association）
**属性**：
- 关联类型、关联强度、创建原因
- 关联的信息或事件
- 审批状态和审批人

## 权限模型设计

### 角色定义（简化设计）
1. **系统管理员**：系统配置、用户管理、权限分配
2. **信息管理员**：信息审核、分发、撤回、闭环、关联管理
3. **部门用户**：信息报送、处理、查看（一人可身兼多职）

**说明**：考虑到实际工作中一个人可能身兼多职，甚至一个部门只有一个人，因此简化角色设计，以部门为基本权限单位。

### 权限矩阵（调整后）
| 操作 | 系统管理员 | 信息管理员 | 部门用户 |
|------|------------|------------|----------|
| 信息报送 | ✓ | ✓ | ✓ |
| 信息审核 | ✓ | ✓ | ✗ |
| 信息分发 | ✓ | ✓ | ✗ |
| 信息撤回 | ✓ | ✓ | ✗ |
| 进展回复 | ✓ | ✓ | ✓ |
| 信息关联 | ✓ | ✓ | 申请 |
| 信息闭环 | ✓ | ✓ | 申请 |
| 重要标记 | ✓ | ✓ | ✗ |

### 数据权限（精确权限模型）

#### 权限类型定义
1. **查看权限**：可以查看信息内容和进展
2. **处理权限**：可以回复进展、申请闭环等操作
3. **管理权限**：可以分发、撤回、审核等管理操作

#### 部门用户权限获取方式
- **全局权限**：系统管理员、信息管理员拥有所有信息的管理权限
- **报送权限**：对本部门报送的信息拥有**查看权限**（不包括处理权限）
- **分发权限**：对分发给本部门的信息拥有**查看权限 + 处理权限**
- **关联权限**：对关联信息拥有**查看权限**（不包括处理权限）

#### 前端展示区分
- **"我部门报送的信息"**：只能查看，不能处理
- **"分发给我部门的信息"**：可以查看和处理
- **"关联信息"**：可以查看进展，标注权限来源

## 业务规则

### 信息处理规则
1. 信息必须经过审核才能分发（除自部门信息）
2. 分发后的信息可以撤回，但必须填写撤回原因并记录在案
3. 撤回后可以重新分发给正确的部门
4. 信息闭环需要管理部门确认或处理部门申请
5. 重要信息必须在规定时间内处理

### 关联管理规则
1. 同一事件的信息可以直接关联
2. 重要关联需要管理部门审批
3. **关联权限传递规则**：
   - **直接权限优先**：如果用户对信息有直接权限（报送或分发），以直接权限为准
   - **关联查看权限**：通过关联获得的权限只包括查看，不包括处理
   - **权限标识清晰**：在界面上清楚标识权限来源
4. **复杂关联场景示例**：
   - X1信息分发给部门A、B（A、B都有处理权限）
   - X2信息只分发给部门A，但关联X1（A有处理权限，B通过关联有查看权限）
   - X3信息只分发给部门B，但关联X1（B有处理权限，A通过关联有查看权限）
5. 关联关系可以解除但需要记录原因

### 进展管理规则
1. 所有处理进展必须记录在案
2. 进展内容不能删除，只能补充
3. 关联信息的进展统一展示
4. 进展查看受权限控制

## 前端界面设计建议

### 信息列表展示
1. **权限标识**：用不同图标或颜色标识权限类型
   - 🔵 可处理（分发给本部门）
   - 🟡 可查看（本部门报送）
   - 🟢 关联查看（通过关联获得）

2. **信息分类展示**：
   - "待我处理"：分发给本部门且未完成的信息
   - "我部门报送"：本部门报送的所有信息
   - "关联信息"：通过关联可查看的信息

### 信息详情页面
1. **权限提示**：明确显示"您对此信息的权限：查看/处理"
2. **关联信息标注**：标注"通过关联获得查看权限"
3. **操作按钮控制**：根据权限显示/隐藏操作按钮

### 关联关系可视化
1. **关联图谱**：显示信息间的关联关系
2. **权限路径**：显示用户通过什么路径获得权限
3. **统一时间线**：关联信息的进展统一展示，标注信息来源

## 业务流程图

### 信息处理主流程
```mermaid
graph TD
    A[信息报送] --> B{是否涉及自身}
    B -->|是| C[自主处理]
    B -->|否| D[管理部门审核]
    D --> E{审核结果}
    E -->|通过| F[分发给相关部门]
    E -->|拒绝| G[通知报送部门]
    F --> H[各部门协同处理]
    C --> I[记录处理进展]
    H --> I
    I --> J{是否完成}
    J -->|是| K[申请闭环]
    J -->|否| I
    K --> L[管理部门审核]
    L --> M[信息闭环]
```

### 信息关联流程
```mermaid
graph TD
    A[新信息创建] --> B[智能关联分析]
    B --> C[生成关联建议]
    C --> D[用户确认关联]
    D --> E{是否需要审批}
    E -->|是| F[提交审批]
    E -->|否| G[关联生效]
    F --> H{审批结果}
    H -->|通过| G
    H -->|拒绝| I[关联失败]
    G --> J[进展共享]
```

## 文档修改记录
| 版本 | 时间 | 修改人 | 修改内容 |
|------|------|--------|----------|
| v1.2 | 2025-06-04 13:51:06 +08:00 | AR,PDM | 精确权限模型：区分报送/处理权限，设计关联权限传递规则，添加前端界面建议 |
| v1.1 | 2025-06-04 13:40:41 +08:00 | AR,PDM | 基于用户反馈调整：简化角色定义、以部门为权限单位、支持撤回功能、澄清关联权限 |
| v1.0 | 2025-06-04 13:14:46 +08:00 | AR,PDM | 创建业务架构设计文档 |
