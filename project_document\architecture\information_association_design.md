# 信息关联机制详细设计

## 概述
信息关联机制是JJWX平台的核心功能，支持同一事件的多条信息关联，实现进展共享和协同处理。

## 关联类型设计

### 1. 直接关联（Information-to-Information）
**定义**：两条信息直接关联，表示它们属于同一事件或高度相关。

**关联类型枚举**：
- `SAME_EVENT`：同一事件的不同信息
- `RELATED_EVENT`：相关事件信息
- `FOLLOW_UP`：后续跟进信息
- `DUPLICATE`：重复信息（需要合并处理）
- `REFERENCE`：引用关系

### 2. 事件关联（Information-to-Event）
**定义**：多条信息关联到同一个事件实体，通过事件统一管理。

**事件类型**：
- 突发事件
- 常规事件
- 专项事件
- 综合事件

## 智能关联算法设计

### 算法架构
```
输入信息 → 文本预处理 → 特征提取 → 相似度计算 → 权重融合 → 关联建议
```

### 1. 文本预处理
**步骤**：
- 分词处理（中文分词）
- 去除停用词
- 提取关键词和实体
- 标准化处理

**技术实现**：
- 使用GoFrame内置字符串处理
- 集成简单的中文分词库
- 建立停用词词典

### 2. 相似度计算维度

#### 2.1 内容相似度（权重：40%）
**算法**：TF-IDF + 余弦相似度
```go
// 伪代码
func calculateContentSimilarity(info1, info2 *Information) float64 {
    keywords1 := extractKeywords(info1.Content)
    keywords2 := extractKeywords(info2.Content)
    
    tfidf1 := calculateTFIDF(keywords1)
    tfidf2 := calculateTFIDF(keywords2)
    
    return cosineSimilarity(tfidf1, tfidf2)
}
```

#### 2.2 时间相关性（权重：25%）
**算法**：时间窗口衰减函数
```go
func calculateTimeSimilarity(info1, info2 *Information) float64 {
    timeDiff := abs(info1.CreatedAt.Sub(info2.CreatedAt).Hours())
    
    // 24小时内权重1.0，之后指数衰减
    if timeDiff <= 24 {
        return 1.0
    }
    return math.Exp(-timeDiff / 168) // 一周半衰期
}
```

#### 2.3 部门相关性（权重：20%）
**算法**：部门关系权重
```go
func calculateDepartmentSimilarity(info1, info2 *Information) float64 {
    if info1.DepartmentID == info2.DepartmentID {
        return 1.0 // 同一部门
    }
    
    if isRelatedDepartment(info1.DepartmentID, info2.DepartmentID) {
        return 0.7 // 相关部门
    }
    
    return 0.3 // 不同部门基础权重
}
```

#### 2.4 关键词匹配（权重：15%）
**算法**：关键词重叠度
```go
func calculateKeywordSimilarity(info1, info2 *Information) float64 {
    keywords1 := extractKeywords(info1.Title + " " + info1.Content)
    keywords2 := extractKeywords(info2.Title + " " + info2.Content)
    
    intersection := intersect(keywords1, keywords2)
    union := union(keywords1, keywords2)
    
    return float64(len(intersection)) / float64(len(union)) // Jaccard相似度
}
```

### 3. 综合评分算法
```go
func calculateAssociationScore(info1, info2 *Information) float64 {
    contentScore := calculateContentSimilarity(info1, info2) * 0.4
    timeScore := calculateTimeSimilarity(info1, info2) * 0.25
    deptScore := calculateDepartmentSimilarity(info1, info2) * 0.2
    keywordScore := calculateKeywordSimilarity(info1, info2) * 0.15
    
    return contentScore + timeScore + deptScore + keywordScore
}
```

## 数据库设计

### 核心表结构

#### 1. information表（信息主表）
```sql
CREATE TABLE information (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(50),
    department_id INTEGER NOT NULL,
    reporter_id INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    priority INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES department(id),
    FOREIGN KEY (reporter_id) REFERENCES user(id)
);
```

#### 2. event表（事件表）
```sql
CREATE TABLE event (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    event_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    created_by INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES user(id)
);
```

#### 3. information_association表（信息关联表）
```sql
CREATE TABLE information_association (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_info_id INTEGER NOT NULL,
    target_info_id INTEGER NOT NULL,
    association_type VARCHAR(20) NOT NULL,
    confidence_score DECIMAL(3,2),
    created_by INTEGER NOT NULL,
    approved_by INTEGER,
    status VARCHAR(20) DEFAULT 'pending',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    approved_at DATETIME,
    FOREIGN KEY (source_info_id) REFERENCES information(id),
    FOREIGN KEY (target_info_id) REFERENCES information(id),
    FOREIGN KEY (created_by) REFERENCES user(id),
    FOREIGN KEY (approved_by) REFERENCES user(id),
    UNIQUE(source_info_id, target_info_id)
);
```

#### 4. event_association表（事件关联表）
```sql
CREATE TABLE event_association (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    information_id INTEGER NOT NULL,
    event_id INTEGER NOT NULL,
    association_type VARCHAR(20) DEFAULT 'belongs_to',
    created_by INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (information_id) REFERENCES information(id),
    FOREIGN KEY (event_id) REFERENCES event(id),
    FOREIGN KEY (created_by) REFERENCES user(id),
    UNIQUE(information_id, event_id)
);
```

#### 5. association_suggestion表（关联建议表）
```sql
CREATE TABLE association_suggestion (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_info_id INTEGER NOT NULL,
    target_info_id INTEGER NOT NULL,
    similarity_score DECIMAL(3,2) NOT NULL,
    suggestion_reason TEXT,
    status VARCHAR(20) DEFAULT 'pending',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    processed_at DATETIME,
    FOREIGN KEY (source_info_id) REFERENCES information(id),
    FOREIGN KEY (target_info_id) REFERENCES information(id)
);
```

## 业务流程设计

### 1. 自动关联建议流程
```
新信息创建 → 触发关联分析 → 计算相似度 → 生成建议 → 通知相关用户
```

### 2. 手动关联流程
```
用户搜索 → 选择关联信息 → 选择关联类型 → 提交申请 → 审批（可选） → 关联生效
```

### 3. 关联审批流程
```
关联申请 → 管理部门审核 → 审批通过/拒绝 → 通知申请人 → 关联生效/失效
```

## 权限控制设计

### 关联操作权限
- **管理部门**：可创建、修改、删除任何关联
- **报送部门**：可申请关联自己报送的信息
- **处理部门**：可申请关联正在处理的信息
- **查看权限**：有任一关联信息查看权限即可查看关联关系

### 进展共享权限
- 用户只能查看自己有权限的信息的进展
- 关联不会扩大用户的信息查看权限
- 进展展示时需要进行权限过滤

## 文档修改记录
| 版本 | 时间 | 修改人 | 修改内容 |
|------|------|--------|----------|
| v1.0 | 2025-06-04 13:14:46 +08:00 | AR,LD | 创建信息关联机制详细设计文档 |
