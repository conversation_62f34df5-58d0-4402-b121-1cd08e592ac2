# JJWX 代码架构设计 v1.0

## 概述
基于GoFrame框架的分层架构设计，采用模块化、可扩展的代码组织方式，遵循SOLID原则和Clean Architecture理念。

## 项目结构设计

### 整体目录结构
```
jjwx/
├── backend/                    # GoFrame后端服务
│   ├── api/                   # API接口层
│   │   ├── v1/               # API版本1
│   │   │   ├── auth/         # 认证相关接口
│   │   │   ├── information/  # 信息管理接口
│   │   │   ├── association/  # 关联管理接口
│   │   │   ├── admin/        # 管理功能接口
│   │   │   ├── user/         # 用户管理接口
│   │   │   └── audit/        # 审计接口
│   │   └── middleware/       # 中间件
│   ├── internal/             # 内部业务逻辑
│   │   ├── controller/       # 控制器层
│   │   ├── service/          # 业务服务层
│   │   ├── dao/              # 数据访问层
│   │   ├── model/            # 数据模型
│   │   ├── logic/            # 业务逻辑
│   │   └── consts/           # 常量定义
│   ├── manifest/             # 配置文件
│   │   ├── config/           # 配置文件
│   │   └── docker/           # Docker配置
│   ├── resource/             # 资源文件
│   │   ├── template/         # 模板文件
│   │   └── public/           # 静态资源
│   ├── utility/              # 工具包
│   │   ├── crypto/           # 加密工具
│   │   ├── validator/        # 验证工具
│   │   └── helper/           # 辅助工具
│   ├── main.go               # 程序入口
│   └── go.mod                # Go模块文件
├── desktop/                  # Wails桌面应用
│   ├── app/                  # Go应用层
│   ├── frontend/             # Vue前端
│   └── build/                # 构建配置
├── web/                      # Vue Web前端
│   ├── src/
│   │   ├── api/              # API调用
│   │   ├── components/       # 组件
│   │   ├── views/            # 页面
│   │   ├── store/            # 状态管理
│   │   ├── router/           # 路由
│   │   ├── utils/            # 工具函数
│   │   └── types/            # 类型定义
│   └── public/
└── shared/                   # 共享代码
    ├── types/                # 类型定义
    ├── constants/            # 常量定义
    └── utils/                # 工具函数
```

## 分层架构设计

### 1. API层 (api/)
**职责**：处理HTTP请求，参数验证，响应格式化
**特点**：
- 薄层设计，只处理HTTP相关逻辑
- 统一的请求/响应格式
- 参数验证和错误处理
- 权限验证中间件

**示例结构**：
```go
// api/v1/information/information.go
type cInformation struct{}

func (c *cInformation) Create(ctx context.Context, req *v1.InformationCreateReq) (res *v1.InformationCreateRes, err error) {
    // 参数验证
    // 调用Service层
    // 返回响应
}
```

### 2. 控制器层 (internal/controller/)
**职责**：业务流程控制，调用Service层
**特点**：
- 业务流程编排
- 事务控制
- 异常处理

### 3. 服务层 (internal/service/)
**职责**：核心业务逻辑实现
**特点**：
- 业务规则实现
- 跨模块协调
- 业务异常处理

**模块划分**：
```go
// service/
├── auth/           # 认证服务
├── information/    # 信息管理服务
├── association/    # 关联管理服务
├── admin/          # 管理功能服务
├── user/           # 用户管理服务
├── audit/          # 审计服务
└── notification/   # 通知服务
```

### 4. 数据访问层 (internal/dao/)
**职责**：数据库操作，数据持久化
**特点**：
- Repository模式
- 数据库事务管理
- 查询优化

### 5. 模型层 (internal/model/)
**职责**：数据结构定义
**分类**：
- **Entity**：数据库实体
- **DTO**：数据传输对象
- **VO**：视图对象

## 核心模块设计

### 1. 认证模块 (auth)
**功能**：
- 用户登录/登出
- JWT Token管理
- 权限验证
- 会话管理

**核心组件**：
```go
type AuthService interface {
    Login(ctx context.Context, req *model.LoginReq) (*model.LoginRes, error)
    Logout(ctx context.Context, token string) error
    ValidateToken(ctx context.Context, token string) (*model.UserInfo, error)
    RefreshToken(ctx context.Context, token string) (*model.TokenInfo, error)
}
```

### 2. 信息管理模块 (information)
**功能**：
- 信息CRUD操作
- 信息审核流程
- 信息分发管理
- 进展记录管理

**核心组件**：
```go
type InformationService interface {
    Create(ctx context.Context, req *model.InformationCreateReq) (*model.Information, error)
    Update(ctx context.Context, req *model.InformationUpdateReq) error
    Delete(ctx context.Context, id int64) error
    GetByID(ctx context.Context, id int64) (*model.Information, error)
    List(ctx context.Context, req *model.InformationListReq) (*model.InformationListRes, error)
    Approve(ctx context.Context, req *model.InformationApproveReq) error
    Distribute(ctx context.Context, req *model.InformationDistributeReq) error
    Withdraw(ctx context.Context, req *model.InformationWithdrawReq) error
}
```

### 3. 关联管理模块 (association)
**功能**：
- 信息关联管理
- 智能关联推荐
- 关联权限控制
- 进展共享

**核心组件**：
```go
type AssociationService interface {
    CreateAssociation(ctx context.Context, req *model.AssociationCreateReq) error
    RemoveAssociation(ctx context.Context, req *model.AssociationRemoveReq) error
    GetAssociations(ctx context.Context, informationID int64) ([]*model.Association, error)
    GenerateSuggestions(ctx context.Context, informationID int64) ([]*model.AssociationSuggestion, error)
    ApproveAssociation(ctx context.Context, req *model.AssociationApproveReq) error
}
```

### 4. 管理功能模块 (admin)
**功能**：
- 分类管理
- 来源渠道管理
- 系统配置管理
- 用户权限管理

### 5. 审计模块 (audit)
**功能**：
- 操作日志记录
- 数据变更追踪
- 审计报告生成
- 合规性检查

## 设计模式应用

### 1. Repository模式
```go
type InformationRepository interface {
    Insert(ctx context.Context, data *entity.Information) (int64, error)
    Update(ctx context.Context, data *entity.Information) error
    Delete(ctx context.Context, id int64) error
    GetByID(ctx context.Context, id int64) (*entity.Information, error)
    List(ctx context.Context, req *model.InformationListReq) ([]*entity.Information, int, error)
}
```

### 2. Factory模式
```go
type ServiceFactory interface {
    CreateInformationService() InformationService
    CreateAssociationService() AssociationService
    CreateAuthService() AuthService
}
```

### 3. Middleware模式
```go
// 认证中间件
func AuthMiddleware() ghttp.HandlerFunc {
    return func(r *ghttp.Request) {
        // 权限验证逻辑
    }
}

// 审计中间件
func AuditMiddleware() ghttp.HandlerFunc {
    return func(r *ghttp.Request) {
        // 审计日志记录
    }
}
```

### 4. Strategy模式
```go
// 关联算法策略
type AssociationStrategy interface {
    Calculate(info1, info2 *entity.Information) float64
}

type TFIDFStrategy struct{}
type KeywordStrategy struct{}
type TimeStrategy struct{}
```

## 依赖关系设计

### 依赖方向
```
API层 → Controller层 → Service层 → DAO层 → Model层
```

### 模块依赖
- **auth模块**：基础模块，被其他模块依赖
- **information模块**：核心模块，依赖auth模块
- **association模块**：依赖information和auth模块
- **admin模块**：依赖auth模块
- **audit模块**：横切模块，记录其他模块的操作

### 接口解耦
```go
// 使用接口解耦模块依赖
type InformationServiceInterface interface {
    // 接口定义
}

// 在其他模块中使用接口而非具体实现
type AssociationService struct {
    informationService InformationServiceInterface
}
```

## 配置管理

### 配置文件结构
```yaml
# manifest/config/config.yaml
server:
  address: ":8080"
  
database:
  type: "sqlite"
  source: "data/jjwx.db"
  
security:
  jwt_secret: "your-secret-key"
  encryption_key: "your-encryption-key"
  
logging:
  level: "info"
  path: "logs/"
```

### 环境配置
- **开发环境**：config.dev.yaml
- **测试环境**：config.test.yaml
- **生产环境**：config.prod.yaml

## 错误处理

### 统一错误码
```go
const (
    CodeSuccess           = 0
    CodeInvalidParams     = 1001
    CodeUnauthorized      = 1002
    CodeForbidden         = 1003
    CodeNotFound          = 1004
    CodeInternalError     = 1005
    CodeBusinessError     = 2001
)
```

### 错误处理机制
```go
type BusinessError struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}

func (e *BusinessError) Error() string {
    return e.Message
}
```

## 性能优化

### 1. 数据库优化
- 连接池配置
- 查询优化
- 索引策略
- 缓存机制

### 2. 内存优化
- 对象池使用
- 内存泄漏防护
- GC优化

### 3. 并发优化
- Goroutine池
- 锁优化
- 异步处理

## 安全设计

### 1. 输入验证
- 参数验证
- SQL注入防护
- XSS防护

### 2. 权限控制
- RBAC权限模型
- API权限验证
- 数据权限过滤

### 3. 数据保护
- 敏感数据加密
- 传输加密
- 存储加密

## 文档修改记录
| 版本 | 时间 | 修改人 | 修改内容 |
|------|------|--------|----------|
| v1.0 | 2025-06-04 14:47:24 +08:00 | AR,LD | 创建代码架构设计文档 |
