# JJWX API规格设计 v1.0

## 概述
完整的RESTful API接口规格设计，包含认证、信息管理、关联管理、管理功能等所有核心模块的API接口。

## API设计原则

### 1. RESTful设计
- 使用标准HTTP方法：GET、POST、PUT、DELETE、PATCH
- 资源导向的URL设计
- 统一的响应格式
- 合理的HTTP状态码

### 2. 版本控制
- URL版本控制：`/api/v1/`
- 向后兼容原则
- 版本废弃策略

### 3. 安全设计
- JWT Token认证
- 权限验证
- 参数验证
- 防护机制

## 通用规范

### 1. 请求格式
```
Content-Type: application/json
Authorization: Bearer {jwt_token}
```

### 2. 响应格式
```json
{
  "code": 0,
  "message": "success",
  "data": {},
  "timestamp": "2025-06-04T14:47:24+08:00"
}
```

### 3. 错误码定义
```json
{
  "0": "成功",
  "1001": "参数错误",
  "1002": "未授权",
  "1003": "权限不足",
  "1004": "资源不存在",
  "1005": "服务器内部错误",
  "2001": "业务逻辑错误",
  "2002": "数据验证失败",
  "2003": "操作冲突",
  "2004": "资源已存在"
}
```

### 4. 分页格式
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

## 1. 认证API (/api/v1/auth)

### 1.1 用户登录
```
POST /api/v1/auth/login
```

**请求体**：
```json
{
  "username": "admin",
  "password": "password123"
}
```

**响应**：
```json
{
  "code": 0,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2025-06-05T14:47:24+08:00",
    "user": {
      "id": 1,
      "username": "admin",
      "real_name": "管理员",
      "department": {
        "id": 1,
        "name": "信息管理部"
      },
      "roles": ["系统管理员"]
    }
  }
}
```

### 1.2 用户登出
```
POST /api/v1/auth/logout
```

### 1.3 刷新Token
```
POST /api/v1/auth/refresh
```

### 1.4 获取当前用户信息
```
GET /api/v1/auth/profile
```

## 2. 信息管理API (/api/v1/information)

### 2.1 创建信息
```
POST /api/v1/information
```

**请求体**：
```json
{
  "content": "某区域发生大面积停电...",
  "summary": "某区域停电事件",
  "official_categories": [1, 7],
  "custom_category": "电力故障",
  "priority": 4,
  "risk_level": "high",
  "source_channel_id": 2,
  "incident_location": "某某区某某街道",
  "deadline": "2025-06-05T18:00:00+08:00",
  "processing_requirements": "立即联系电力部门抢修",
  "remarks": "已通知相关部门"
}
```

**响应**：
```json
{
  "code": 0,
  "message": "信息创建成功",
  "data": {
    "id": 1001,
    "status": "pending"
  }
}
```

### 2.2 获取信息列表
```
GET /api/v1/information?page=1&size=20&status=pending&category=1&keyword=停电
```

**响应**：
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1001,
        "content": "某区域发生大面积停电...",
        "summary": "某区域停电事件",
        "official_categories": [
          {"id": 1, "name": "突发事件"},
          {"id": 7, "name": "安全隐患"}
        ],
        "custom_category": "电力故障",
        "priority": 4,
        "risk_level": "high",
        "status": "pending",
        "reporter": {
          "id": 1001,
          "real_name": "张三",
          "department": "技术部"
        },
        "source_channel": {
          "id": 2,
          "name": "热线电话"
        },
        "created_at": "2025-06-04T14:47:24+08:00"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 1,
      "pages": 1
    }
  }
}
```

### 2.3 获取信息详情
```
GET /api/v1/information/{id}
```

### 2.4 更新信息
```
PUT /api/v1/information/{id}
```

### 2.5 删除信息
```
DELETE /api/v1/information/{id}
```

### 2.6 审核信息
```
POST /api/v1/information/{id}/approve
```

**请求体**：
```json
{
  "action": "approve",
  "comment": "审核通过"
}
```

### 2.7 分发信息
```
POST /api/v1/information/{id}/distribute
```

**请求体**：
```json
{
  "department_ids": [2, 3],
  "comment": "请协助处理"
}
```

### 2.8 撤回信息
```
POST /api/v1/information/{id}/withdraw
```

**请求体**：
```json
{
  "department_ids": [2],
  "reason": "分发错误"
}
```

### 2.9 闭环信息
```
POST /api/v1/information/{id}/close
```

**请求体**：
```json
{
  "reason": "问题已解决"
}
```

## 3. 进展管理API (/api/v1/progress)

### 3.1 添加进展
```
POST /api/v1/progress
```

**请求体**：
```json
{
  "information_id": 1001,
  "content": "已联系电力部门，正在抢修中",
  "progress_type": "reply",
  "is_internal": false
}
```

### 3.2 获取进展列表
```
GET /api/v1/progress?information_id=1001
```

### 3.3 更新进展
```
PUT /api/v1/progress/{id}
```

### 3.4 删除进展
```
DELETE /api/v1/progress/{id}
```

## 4. 关联管理API (/api/v1/association)

### 4.1 创建关联
```
POST /api/v1/association
```

**请求体**：
```json
{
  "source_info_id": 1001,
  "target_info_id": 1002,
  "association_type": "SAME_EVENT",
  "reason": "同一停电事件的不同报告"
}
```

### 4.2 获取关联建议
```
GET /api/v1/association/suggestions/{information_id}
```

**响应**：
```json
{
  "code": 0,
  "message": "success",
  "data": [
    {
      "target_info_id": 1002,
      "similarity_score": 0.85,
      "suggestion_reason": "内容相似度高，时间接近",
      "target_info": {
        "id": 1002,
        "summary": "某区域电力故障",
        "created_at": "2025-06-04T14:30:00+08:00"
      }
    }
  ]
}
```

### 4.3 获取关联信息
```
GET /api/v1/association/{information_id}
```

### 4.4 删除关联
```
DELETE /api/v1/association/{id}
```

### 4.5 审批关联
```
POST /api/v1/association/{id}/approve
```

## 5. 管理功能API (/api/v1/admin)

### 5.1 分类管理

#### 5.1.1 获取分类列表
```
GET /api/v1/admin/categories
```

#### 5.1.2 创建分类
```
POST /api/v1/admin/categories
```

**请求体**：
```json
{
  "name": "网络安全",
  "description": "网络安全相关事件",
  "sort_order": 10
}
```

#### 5.1.3 更新分类
```
PUT /api/v1/admin/categories/{id}
```

#### 5.1.4 删除分类（禁用）
```
DELETE /api/v1/admin/categories/{id}
```

#### 5.1.5 批量排序
```
PUT /api/v1/admin/categories/sort
```

### 5.2 来源渠道管理

#### 5.2.1 获取渠道列表
```
GET /api/v1/admin/source-channels
```

#### 5.2.2 创建渠道
```
POST /api/v1/admin/source-channels
```

#### 5.2.3 更新渠道
```
PUT /api/v1/admin/source-channels/{id}
```

#### 5.2.4 删除渠道（禁用）
```
DELETE /api/v1/admin/source-channels/{id}
```

### 5.3 用户管理

#### 5.3.1 获取用户列表
```
GET /api/v1/admin/users?page=1&size=20&department_id=1&status=active
```

#### 5.3.2 创建用户
```
POST /api/v1/admin/users
```

#### 5.3.3 更新用户
```
PUT /api/v1/admin/users/{id}
```

#### 5.3.4 重置密码
```
POST /api/v1/admin/users/{id}/reset-password
```

### 5.4 部门管理

#### 5.4.1 获取部门列表
```
GET /api/v1/admin/departments
```

#### 5.4.2 创建部门
```
POST /api/v1/admin/departments
```

#### 5.4.3 更新部门
```
PUT /api/v1/admin/departments/{id}
```

## 6. 审计API (/api/v1/audit)

### 6.1 获取操作日志
```
GET /api/v1/audit/logs?page=1&size=20&user_id=1&action=create&start_date=2025-06-01&end_date=2025-06-04
```

### 6.2 获取数据变更日志
```
GET /api/v1/audit/changes?page=1&size=20&table_name=information&record_id=1001
```

### 6.3 导出审计报告
```
POST /api/v1/audit/export
```

## 权限验证

### 1. Token验证
所有API（除登录外）都需要在Header中携带JWT Token：
```
Authorization: Bearer {jwt_token}
```

### 2. 权限验证
每个API都会验证用户是否有相应的权限：
```json
{
  "code": 1003,
  "message": "权限不足",
  "data": null
}
```

### 3. 数据权限
根据用户的部门和角色，过滤返回的数据：
- 部门用户只能看到本部门相关的信息
- 管理员可以看到所有信息

## 错误处理

### 1. 参数验证错误
```json
{
  "code": 1001,
  "message": "参数错误",
  "data": {
    "errors": [
      {
        "field": "content",
        "message": "内容不能为空"
      }
    ]
  }
}
```

### 2. 业务逻辑错误
```json
{
  "code": 2001,
  "message": "信息已经审核通过，无法修改",
  "data": null
}
```

### 3. 系统错误
```json
{
  "code": 1005,
  "message": "服务器内部错误",
  "data": null
}
```

## 性能优化

### 1. 分页查询
所有列表接口都支持分页：
```
?page=1&size=20
```

### 2. 字段过滤
支持指定返回字段：
```
?fields=id,title,status,created_at
```

### 3. 缓存策略
- 分类和渠道数据缓存
- 用户权限信息缓存
- 静态配置数据缓存

## 文档修改记录
| 版本 | 时间 | 修改人 | 修改内容 |
|------|------|--------|----------|
| v1.0 | 2025-06-04 14:47:24 +08:00 | AR,LD | 创建API规格设计文档 |
