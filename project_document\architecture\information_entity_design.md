# 信息实体（Information）简化设计

## 概述
信息实体是JJWX平台的核心数据模型，采用简洁实用的设计原则，避免过度复杂化。

## 简化属性设计

### 1. 核心基础属性
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INTEGER | PRIMARY KEY | 信息唯一标识 |
| content | TEXT | NOT NULL | 信息详细内容 |
| summary | VARCHAR(500) | NULL | 信息摘要 |

### 2. 分类和标识属性
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| official_categories | TEXT | NOT NULL | 上级规定分类（JSON数组，多选） |
| custom_category | VARCHAR(100) | NULL | 自定义分类（可选） |
| priority | INTEGER | DEFAULT 3 | 优先级（1-5，5最高） |
| risk_level | VARCHAR(20) | DEFAULT 'low' | 风险等级（低、中、高） |

### 3. 时间相关属性
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 更新时间 |
| deadline | DATETIME | NULL | 处理截止时间 |
| closed_at | DATETIME | NULL | 闭环时间 |

### 4. 人员和部门属性
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| reporter_id | INTEGER | NOT NULL | 报送人ID |
| reporter_department_id | INTEGER | NOT NULL | 报送部门ID |
| source_channel | VARCHAR(50) | NULL | 信息来源渠道 |

### 5. 地理位置属性
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| incident_location | VARCHAR(255) | NULL | 涉事地 |

### 6. 状态和处理属性
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| status | VARCHAR(20) | DEFAULT 'pending' | 信息状态 |
| processing_status | VARCHAR(20) | DEFAULT 'not_started' | 处理状态 |
| is_closed | BOOLEAN | DEFAULT FALSE | 是否已闭环 |
| close_reason | VARCHAR(255) | NULL | 闭环原因 |

### 7. 业务相关属性
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| processing_requirements | TEXT | NULL | 处理要求 |
| remarks | TEXT | NULL | 备注说明 |

### 8. 扩展属性
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| custom_fields | TEXT | NULL | 自定义字段（JSON格式） |

## 枚举值定义

### 上级规定分类（official_categories）
**说明**：存储为JSON数组，包含分类ID，支持多选，如：`[1, 3, 5]`
通过关联official_category表获取分类名称，支持动态管理。

### 风险等级（risk_level）
```
- low: 低风险
- medium: 中风险
- high: 高风险
```

### 信息状态（status）
```
- pending: 待审核
- approved: 审核通过
- rejected: 审核拒绝
- distributed: 已分发
- processing: 处理中
- completed: 处理完成
- closed: 已闭环
```

### 处理状态（processing_status）
```
- not_started: 未开始
- in_progress: 进行中
- pending_review: 待审核
- completed: 已完成
- on_hold: 暂停
- cancelled: 已取消
```

### 来源渠道（source_channel）
```
- department_report: 部门报送
- hotline: 热线电话
- online_platform: 网络平台
- letter: 来信来访
- media: 媒体报道
- inspection: 检查发现
- other: 其他渠道
```

## 数据库表结构（SQLite）

```sql
CREATE TABLE information (
    -- 核心基础属性
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    content TEXT NOT NULL,
    summary VARCHAR(500),

    -- 分类和标识属性
    official_categories TEXT NOT NULL, -- JSON数组，如：["突发事件", "安全隐患"]
    custom_category VARCHAR(100),
    priority INTEGER DEFAULT 3 CHECK (priority >= 1 AND priority <= 5),
    risk_level VARCHAR(20) DEFAULT 'low',

    -- 时间相关属性
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deadline DATETIME,
    closed_at DATETIME,

    -- 人员和部门属性
    reporter_id INTEGER NOT NULL,
    reporter_department_id INTEGER NOT NULL,
    source_channel VARCHAR(50),

    -- 地理位置属性
    incident_location VARCHAR(255),

    -- 状态和处理属性
    status VARCHAR(20) DEFAULT 'pending',
    processing_status VARCHAR(20) DEFAULT 'not_started',
    is_closed BOOLEAN DEFAULT FALSE,
    close_reason VARCHAR(255),

    -- 业务相关属性
    processing_requirements TEXT,
    remarks TEXT,

    -- 扩展属性
    custom_fields TEXT, -- JSON格式

    -- 外键约束
    FOREIGN KEY (reporter_id) REFERENCES user(id),
    FOREIGN KEY (reporter_department_id) REFERENCES department(id)
);

-- 创建索引优化查询性能
CREATE INDEX idx_information_status ON information(status);
CREATE INDEX idx_information_risk_level ON information(risk_level);
CREATE INDEX idx_information_department ON information(reporter_department_id);
CREATE INDEX idx_information_created_at ON information(created_at);
CREATE INDEX idx_information_priority ON information(priority);
```

## 设计说明

### 1. 简化原则
- **去除冗余字段**：删除标题、联系信息、详细地理信息等
- **合并相似概念**：风险等级替代紧急程度和重要程度
- **保留核心功能**：确保6个业务场景仍能完整支持

### 2. 分类系统设计
- **上级规定分类**：JSON数组存储，支持多选，必填
- **自定义分类**：VARCHAR字段，单选，可选
- **灵活扩展**：可根据实际需要调整分类列表

### 3. 性能优化
- **精简索引**：只为常用查询字段创建索引
- **合理约束**：优先级1-5级，风险等级3级
- **JSON支持**：利用SQLite的JSON功能处理分类

### 4. 扩展性保留
- **custom_fields**：JSON字段支持未来扩展
- **模块化设计**：属性分组清晰，便于维护

## 使用示例

### 创建信息记录
```sql
INSERT INTO information (
    content, summary, official_categories, custom_category,
    priority, risk_level, reporter_id, reporter_department_id,
    source_channel, incident_location, deadline,
    processing_requirements, remarks
) VALUES (
    '某区域发生大面积停电，影响居民生活，需要紧急处理...',
    '某区域停电事件',
    '["突发事件", "安全隐患"]',
    '电力故障',
    4, 'high',
    1001, 2001, 'hotline',
    '某某区某某街道',
    '2025-06-05 18:00:00',
    '立即联系电力部门抢修，确保24小时内恢复供电',
    '已通知相关部门，正在协调处理'
);
```

### 查询特定分类的信息
```sql
-- 查询包含"突发事件"分类的信息
SELECT * FROM information
WHERE JSON_EXTRACT(official_categories, '$') LIKE '%突发事件%';

-- 查询高风险信息
SELECT * FROM information
WHERE risk_level = 'high'
ORDER BY created_at DESC;
```

## 文档修改记录
| 版本 | 时间 | 修改人 | 修改内容 |
|------|------|--------|----------|
| v2.0 | 2025-06-04 14:09:01 +08:00 | AR,LD | 基于用户反馈大幅简化设计：去除标题、联系信息等，合并风险等级，重新设计分类系统 |
| v1.0 | 2025-06-04 13:57:11 +08:00 | AR,LD | 创建信息实体详细设计文档 |
